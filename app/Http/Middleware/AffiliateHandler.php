<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Cookie;

class AffiliateHandler
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->has('ref')) {
            $refCode = $request->get('ref');

            // set cookie ghi đè
            Cookie::queue('affiliate_ref', $refCode, 60 * 24 * 365 * 10);

            // Chỉ tăng click nếu là request chính và ref code là hex hợp lệ (cho affiliate system)
            if ($request->isMethod('GET') && !$request->ajax() && !$request->is('assets/*')) {
                // Kiểm tra xem ref code có phải là hex string không (cho affiliate system)
                if (ctype_xdigit($refCode)) {
                    try {
                        $decodedRefCode = hex2bin($refCode);
                        $aff_id = str_replace('KH-', '', $decodedRefCode);

                        if (is_numeric($aff_id)) {
                            $affiliater = User::find($aff_id);

                            if ($affiliater) {
                                // Sử dụng cookie thay vì session để tránh lỗi
                                $clickKey = 'affiliate_click_' . $aff_id;
                                if (!$request->cookie($clickKey)) {
                                    $affiliater->increment('total_clicks');
                                    Cookie::queue($clickKey, true, 60); // Cookie tồn tại trong 1 giờ
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        // Bỏ qua lỗi hex2bin cho các ref code không phải hex
                    }
                }
            }
        }

        return $next($request);
    }
}

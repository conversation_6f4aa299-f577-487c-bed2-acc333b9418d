<?php

use App\Http\Controllers\CommonController;
use App\Http\Controllers\InstallController;
use App\Http\Controllers\ModalController;
use App\Http\Controllers\PageController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\frontend\HomeController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\EmbedController;
use App\Http\Controllers\Admin\UserInquiryController;

//Cache clear route
Route::get('/clear-cache', function () {
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    Artisan::call('route:clear');
    Artisan::call('view:clear');
    Cache::flush();

    return 'Application cache cleared';
});

// Test referral tracking routes
Route::get('/test-referral', function () {
    $refCode = request()->get('ref', 'SPEAKPRO-073');
    return view('test-referral', compact('refCode'));
})->name('test.referral');

Route::get('/test-middleware', function () {
    return view('test-middleware');
})->name('test.middleware');

Route::get('/test-register-form', function () {
    $refCode = request()->get('ref');
    return view('test-register-form', compact('refCode'));
})->name('test.register.form');

Route::get('/debug-ref', function () {
    $refCode = request()->get('ref', '');

    $debug = [
        'ref_code' => $refCode,
        'is_hex' => ctype_xdigit($refCode),
        'length' => strlen($refCode),
        'is_even_length' => strlen($refCode) % 2 === 0,
        'can_decode' => false,
        'decoded_value' => null,
        'is_affiliate_format' => false,
    ];

    if (ctype_xdigit($refCode) && strlen($refCode) % 2 === 0) {
        try {
            $decoded = hex2bin($refCode);
            $debug['can_decode'] = true;
            $debug['decoded_value'] = $decoded;
            $debug['is_affiliate_format'] = strpos($decoded, 'KH-') === 0;
        } catch (\Exception $e) {
            $debug['decode_error'] = $e->getMessage();
        }
    }

    return response()->json($debug, 200, [], JSON_PRETTY_PRINT);
})->name('debug.ref');

Route::get('home/switch/{id}', [HomeController::class, 'homepage_switcher'])->name('home.switch');

//Redirect route
Route::get('/dashboard', function () {
    if (auth()->user()->role == 'admin') {
        return redirect(route('admin.dashboard'));
    }elseif(auth()->user()->role == 'student'){
        return redirect(route('my.courses'));
    } else {
        return redirect(route('home'));
    }
})->middleware(['auth', 'verified'])->name('dashboard');

//Common modal route
Route::get('modal/{view_path}', [ModalController::class, 'common_view_function'])->name('modal');
Route::any('get-video-details/{url?}', [CommonController::class, 'get_video_details'])->name('get.video.details');
Route::get('view/{path}', [CommonController::class, 'rendered_view'])->name('view');

Route::get('closed_back_to_mobile_ber', function () {
    session()->forget('app_url');
    return redirect()->back();
})->name('closed_back_to_mobile_ber');

//Mobile payment redirect
Route::get('payment/web_redirect_to_pay_fee', [PaymentController::class, 'webRedirectToPayFee'])->name('payment.web_redirect_to_pay_fee');

// AJAX endpoint for landing page form submissions
Route::post('api/user-inquiry', [UserInquiryController::class, 'store'])->name('api.user.inquiry.store');


// Account inactive route
Route::get('/account-inactive', function () {
    return view('auth.account-inactive');
})->name('account.inactive');
//Installation routes
Route::get('/violation',function (){
    return view('errors.451');
})->name('violation');

// Device limitation route
Route::get('/device-limitation',function (){
    return view('errors.421');
})->name('device.limitation');

// Route để lấy popup ngẫu nhiên cho video player
Route::get('/get-random-popup', [App\Http\Controllers\Admin\MarketingController::class, 'getRandomPopup'])->name('get.random.popup');

// CRM Routes
Route::group(['middleware' => ['auth', 'verified'], 'prefix' => 'admin'], function() {
    // API routes cho CRM
    Route::post('/crm/activities', [App\Http\Controllers\CrmController::class, 'storeActivity'])->name('admin.crm.store.activity');
    Route::get('/crm/activities/{userId}', [App\Http\Controllers\CrmController::class, 'getActivities'])->name('admin.crm.get.activities');
    Route::post('/crm/opportunities', [App\Http\Controllers\CrmController::class, 'storeOpportunity'])->name('admin.crm.store.opportunity');
    Route::get('/crm/opportunities/{userId}', [App\Http\Controllers\CrmController::class, 'getOpportunities'])->name('admin.crm.get.opportunities');
    Route::match(['put', 'post'], '/crm/opportunities/{id}/status', [App\Http\Controllers\CrmController::class, 'updateOpportunityStatus'])->name('admin.crm.update.opportunity.status');

    // Report routes
    Route::get('/crm/performance', [App\Http\Controllers\CrmController::class, 'salesPerformance'])->name('admin.crm.performance');
    Route::get('/crm/funnel', [App\Http\Controllers\CrmController::class, 'salesFunnel'])->name('admin.crm.funnel');
});

// Embeddable Registration Form
Route::middleware(['cors'])->group(function() {
    Route::get('/embed/register/{course_slug}', [EmbedController::class, 'showEmbedRegistrationForm'])
        ->name('embed.registration')
        ->middleware('allow-embedding');
    Route::post('/embed/register/{course_slug}', [EmbedController::class, 'processEmbedRegistration'])->name('embed.register.process');
});

// Page routes - must be at the end to avoid conflicts
Route::get('/{slug}', [PageController::class, 'show'])->name('page.show')->where('slug', '[a-zA-Z0-9\-_]+');

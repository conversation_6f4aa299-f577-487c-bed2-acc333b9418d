<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Referral Tracking</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Test Referral Tracking System</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>Referral Code hiện tại:</strong> {{ $refCode ?? 'Không có' }}
                        </div>
                        
                        <h5>Các link test:</h5>
                        <ul class="list-group mb-4">
                            <li class="list-group-item">
                                <a href="{{ route('test.register.form', ['ref' => 'SPEAKPRO-073']) }}" target="_blank">
                                    Test đăng ký với ref=SPEAKPRO-073
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="{{ route('test.register.form', ['ref' => 'organic-fanpage-click']) }}" target="_blank">
                                    Test đăng ký với ref=organic-fanpage-click
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="{{ route('test.register.form', ['ref' => 'organic-search-click']) }}" target="_blank">
                                    Test đăng ký với ref=organic-search-click
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="{{ route('test.register.form', ['ref' => 'direct']) }}" target="_blank">
                                    Test đăng ký với ref=direct
                                </a>
                            </li>
                        </ul>
                        
                        <div class="alert alert-warning">
                            <strong>Hướng dẫn test:</strong><br>
                            1. Click vào một trong các link trên<br>
                            2. Đăng ký tài khoản mới<br>
                            3. Vào Admin → Quản lý học viên CRM để xem cột Referral<br>
                            4. Kiểm tra xem referral source đã được lưu đúng chưa
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

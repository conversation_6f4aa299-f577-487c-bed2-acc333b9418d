# Hướng dẫn sử dụng tính năng Referral Tracking

## Tổng quan
Tính năng Referral Tracking cho phép ghi nhận nguồn đăng ký của học viên thông qua parameter `?ref=` trong URL.

## Cách sử dụng

### 1. Thêm parameter ref vào URL
Khi chia sẻ link, thêm parameter `?ref=` với mã referral:

```
https://yourdomain.com/register?ref=SPEAKPRO-073
https://yourdomain.com/course/khoa-hoc-abc?ref=organic-fanpage-click
https://yourdomain.com/any-page?ref=organic-search-click
```

### 2. Các loại referral code phổ biến
- `SPEAKPRO-073` - Mã affiliate cụ thể
- `organic-fanpage-click` - Từ fanpage Facebook
- `organic-search-click` - Từ tìm kiếm Google
- `direct` - <PERSON><PERSON><PERSON> cập trực tiếp
- `email-campaign` - Từ email marketing
- `youtube-video` - Từ video YouTube

### 3. <PERSON>em thống kê referral
1. Đăng nhập Admin
2. Vào **Quản lý học viên CRM**
3. Xem cột **Referral** để thấy nguồn đăng ký của từng học viên

### 4. Cách hoạt động
- Khi user truy cập URL có `?ref=`, hệ thống lưu referral code vào cookie
- Khi user đăng ký, referral code được lưu vào cột `source` trong database
- Referral code được hiển thị với màu sắc khác nhau trong Admin

### 5. Test tính năng
Truy cập các URL test sau:
- `/test-middleware?ref=4b482d3738` - Test middleware hoạt động
- `/test-referral?ref=SPEAKPRO-073` - Test giao diện referral
- `/test-register-form?ref=organic-fanpage-click` - Test form đăng ký
- `/debug-ref?ref=4b482d3738` - Debug ref code processing

## Kỹ thuật

### Database
- Cột `source` trong bảng `users` lưu referral code
- Middleware `AffiliateHandler` xử lý parameter `?ref=`

### Controllers được cập nhật
- `RegisteredUserController` - Đăng ký thông thường
- `EmbedController` - Đăng ký từ embed form

### Files được thay đổi
1. `resources/views/admin/student/index.blade.php` - Thay cột "Follow-up tiếp theo" thành "Referral"
2. `app/Http/Controllers/Auth/RegisteredUserController.php` - Lưu referral source
3. `app/Http/Controllers/EmbedController.php` - Lưu referral source
4. `app/Http/Kernel.php` - Thêm AffiliateHandler vào web middleware

## Lưu ý
- Referral code được lưu trong cookie với thời hạn 10 năm
- Nếu có nhiều referral code, code mới nhất sẽ ghi đè code cũ
- Hệ thống ưu tiên: URL parameter > Cookie affiliate_ref
- Middleware chỉ xử lý affiliate tracking cho mã hex hợp lệ (tránh lỗi hex2bin)
- Các referral code thông thường (SPEAKPRO-073, organic-fanpage-click) vẫn được lưu bình thường

## Sửa lỗi đã thực hiện
- **Lỗi hex2bin()**: Đã thêm kiểm tra `ctype_xdigit()` và độ dài chẵn
- **Kiểm tra format**: Chỉ xử lý affiliate nếu decode thành 'KH-{number}'
- **Try-catch**: Bảo vệ khỏi lỗi decode cho các referral code không phải affiliate hex
- **Validation**: Kiểm tra `is_numeric()` cho affiliate ID trước khi query database
- **Logging**: Thêm log để debug các ref code không phải affiliate

## Debug
Sử dụng `/debug-ref?ref=YOUR_CODE` để kiểm tra:
- Ref code có phải hex không
- Có thể decode được không
- Có đúng format affiliate 'KH-{number}' không

# Hướng dẫn sử dụng tính năng Referral Tracking

## Tổng quan
Tính năng Referral Tracking cho phép ghi nhận nguồn đăng ký của học viên thông qua parameter `?ref=` trong URL.

## Cách sử dụng

### 1. Thêm parameter ref vào URL
Khi chia sẻ link, thêm parameter `?ref=` với mã referral:

```
https://yourdomain.com/register?ref=SPEAKPRO-073
https://yourdomain.com/course/khoa-hoc-abc?ref=organic-fanpage-click
https://yourdomain.com/any-page?ref=organic-search-click
```

### 2. Các loại referral code phổ biến
- `SPEAKPRO-073` - Mã affiliate cụ thể
- `organic-fanpage-click` - Từ fanpage Facebook
- `organic-search-click` - Từ tìm kiếm Google
- `direct` - <PERSON><PERSON><PERSON> cập trực tiếp
- `email-campaign` - Từ email marketing
- `youtube-video` - Từ video YouTube

### 3. <PERSON>em thống kê referral
1. Đăng nhập Admin
2. Vào **Quản lý học viên CRM**
3. Xem cột **Referral** để thấy nguồn đăng ký của từng học viên

### 4. Cách hoạt động
- Khi user truy cập URL có `?ref=`, hệ thống lưu referral code vào cookie
- Khi user đăng ký, referral code được lưu vào cột `source` trong database
- Referral code được hiển thị với màu sắc khác nhau trong Admin

### 5. Test tính năng
Truy cập `/test-referral` để test các link referral khác nhau:
- `/test-referral?ref=SPEAKPRO-073`
- `/test-register-form?ref=organic-fanpage-click`

## Kỹ thuật

### Database
- Cột `source` trong bảng `users` lưu referral code
- Middleware `AffiliateHandler` xử lý parameter `?ref=`

### Controllers được cập nhật
- `RegisteredUserController` - Đăng ký thông thường
- `EmbedController` - Đăng ký từ embed form

### Files được thay đổi
1. `resources/views/admin/student/index.blade.php` - Thay cột "Follow-up tiếp theo" thành "Referral"
2. `app/Http/Controllers/Auth/RegisteredUserController.php` - Lưu referral source
3. `app/Http/Controllers/EmbedController.php` - Lưu referral source
4. `app/Http/Kernel.php` - Thêm AffiliateHandler vào web middleware

## Lưu ý
- Referral code được lưu trong cookie với thời hạn 10 năm
- Nếu có nhiều referral code, code mới nhất sẽ ghi đè code cũ
- Hệ thống ưu tiên: URL parameter > Cookie affiliate_ref
